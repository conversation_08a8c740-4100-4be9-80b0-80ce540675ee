{"compilerOptions": {"target": "esnext", "allowJs": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "jsx": "react", "rootDir": "./resources/js", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "paths": {"@/*": ["./resources/js/*"]}, "types": ["vite/client"], "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["resources/js", "vite-env.d.ts"]}