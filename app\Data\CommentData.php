<?php

namespace App\Data;

use Spa<PERSON>\LaravelData\Attributes\Validation\Exists;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

class CommentData extends Data
{
    public function __construct(
        #[Required, Max(1000)]
        public string $comment,

        #[Required, Exists('posts', 'id')]
        public int $post_id,

        #[Nullable, Exists('comments', 'id')]
        public ?int $parent_id
    ) {}
}
